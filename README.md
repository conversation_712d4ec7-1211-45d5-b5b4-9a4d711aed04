# LangGraph Research Agent - CLI Version

A powerful research agent built with LangGraph that can perform comprehensive research on any topic and find annual reports for power plants. This is a terminal-based application with no web interface.

## Features

### 🔬 **General Research Agent**
- Performs comprehensive research on any topic
- Uses multiple search queries and iterative refinement
- Provides well-sourced, detailed answers
- Supports configurable research depth and iterations

### 🏭 **Power Plant Research**
- Searches for power plant holding companies
- Finds and extracts annual reports (PDF links)
- Supports various power plant types and international companies
- Provides direct download links to financial documents

## Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd gemini-fullstack-langgraph-quickstart
   ```

2. **Set up Python environment:**
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -e .
   ```

3. **Set up environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env and add your GEMINI_API_KEY
   ```

## Usage

### General Research

```bash
# Basic research query
python research_agent.py "What are the latest developments in renewable energy?"

# With custom parameters
python research_agent.py "Climate change impacts on agriculture" --initial-queries 5 --max-loops 3

# Using a specific model
python research_agent.py "AI in healthcare" --reasoning-model gemini-2.0-flash-exp
```

### Power Plant Research

```bash
# Search for power plant annual reports
python research_agent.py "Vogtle Nuclear Plant"
python research_agent.py "Seil Power Plant"
python research_agent.py "Three Mile Island"
```

### Command Line Options

```bash
python research_agent.py --help
```

**Arguments:**
- `question`: Research question or power plant name
- `--initial-queries`: Number of initial search queries (default: 3)
- `--max-loops`: Maximum number of research loops (default: 2)
- `--reasoning-model`: Model for final answer (default: gemini-2.5-pro-preview-05-06)

## Examples

### 1. General Research
```bash
python research_agent.py "What are the environmental impacts of lithium mining?"
```

### 2. Power Plant Research
```bash
python research_agent.py "Vogtle Nuclear Plant"
```
**Output includes:**
- Power plant identification
- Holding company information
- Direct PDF links to annual reports
- Financial document summaries

### 3. Deep Research
```bash
python research_agent.py "Future of fusion energy" --initial-queries 5 --max-loops 3
```

## Environment Setup

Create a `.env` file in the backend directory:

```env
GEMINI_API_KEY=your_gemini_api_key_here
```

## Project Structure

```
backend/
├── src/agent/
│   ├── graph.py              # Main LangGraph workflow
│   ├── power_plant_graph.py  # Power plant search nodes
│   ├── power_plant_search.py # Power plant search engine
│   ├── prompts.py            # LLM prompts
│   ├── state.py              # Graph state definitions
│   ├── tools_and_schemas.py  # Tools and data schemas
│   └── utils.py              # Utility functions
├── research_agent.py         # Main CLI entry point
├── pyproject.toml           # Project dependencies
└── .env                     # Environment variables
```

## How It Works

### General Research Flow
1. **Query Generation**: Creates multiple focused search queries
2. **Web Research**: Performs searches and gathers information
3. **Reflection**: Evaluates research quality and identifies gaps
4. **Iteration**: Performs additional research if needed
5. **Synthesis**: Combines findings into comprehensive answer

### Power Plant Research Flow
1. **Name Extraction**: Identifies power plant from user input
2. **Holding Company Search**: Finds parent company using LLM + web search
3. **Website Discovery**: Locates official company websites
4. **Report Extraction**: Scrapes investor relations pages for annual reports
5. **Result Formatting**: Provides organized results with PDF links

## Requirements

- Python 3.11+
- Gemini API key (Google AI Studio)
- Internet connection for web searches

## License

This project is licensed under the MIT License.