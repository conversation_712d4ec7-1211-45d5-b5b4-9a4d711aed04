#!/usr/bin/env python3
"""
Setup script for the Research Agent CLI
"""

import os
import sys
import subprocess

def main():
    """Setup the research agent CLI"""
    
    print("🔧 Setting up Research Agent CLI...")
    
    # Check Python version
    if sys.version_info < (3, 11):
        print("❌ Python 3.11+ is required")
        sys.exit(1)
    
    print("✅ Python version check passed")
    
    # Change to backend directory
    backend_dir = os.path.join(os.path.dirname(__file__), 'backend')
    os.chdir(backend_dir)
    
    # Install dependencies
    print("📦 Installing dependencies...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-e', '.'], check=True)
        print("✅ Dependencies installed successfully")
    except subprocess.CalledProcessError:
        print("❌ Failed to install dependencies")
        sys.exit(1)
    
    # Check for .env file
    env_file = os.path.join(backend_dir, '.env')
    env_example = os.path.join(backend_dir, '.env.example')
    
    if not os.path.exists(env_file):
        if os.path.exists(env_example):
            print("📄 Creating .env file from example...")
            with open(env_example, 'r') as f:
                content = f.read()
            with open(env_file, 'w') as f:
                f.write(content)
            print("✅ .env file created")
        else:
            print("📄 Creating .env file...")
            with open(env_file, 'w') as f:
                f.write("GEMINI_API_KEY=your_gemini_api_key_here\n")
            print("✅ .env file created")
        
        print("⚠️  Please edit the .env file and add your GEMINI_API_KEY")
    else:
        print("✅ .env file already exists")
    
    print("\n🎉 Setup complete!")
    print("\nNext steps:")
    print("1. Edit backend/.env and add your GEMINI_API_KEY")
    print("2. Run: cd backend && python research_agent.py \"your research question\"")
    print("\nExample:")
    print("cd backend && python research_agent.py \"What are the latest developments in renewable energy?\"")

if __name__ == "__main__":
    main()