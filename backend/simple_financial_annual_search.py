#!/usr/bin/env python3
"""
Simple Financial/Annual Search Engine
Exactly as requested: "if you found financial or annual any where in the link just return that links as output"
"""

import os
import requests
from typing import List, Dict, Optional, Any
from dataclasses import dataclass
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import time
from google.genai import Client
from langchain_google_genai import Chat<PERSON><PERSON>gleGenerativeAI
from langchain_core.messages import HumanMessage
from dotenv import load_dotenv

load_dotenv()

@dataclass
class SimpleSearchResult:
    """Simple search result containing financial/annual links"""
    power_plant_name: str
    holding_company: str
    financial_annual_links: List[str]
    success: bool
    error_message: Optional[str] = None

class SimpleFinancialAnnualSearchEngine:
    """
    Simple search engine that returns any links containing 'financial' or 'annual'
    """
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.getenv("GEMINI_API_KEY")
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY is required")
        
        self.genai_client = Client(api_key=self.api_key)
        
        # Headers for web scraping
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

    def search_financial_annual_links(self, power_plant_name: str) -> SimpleSearchResult:
        """
        Main search function - returns any links containing 'financial' or 'annual'
        """
        try:
            print(f"🔍 Searching for: {power_plant_name}")
            
            # Step 1: Find holding company
            holding_company = self._find_holding_company(power_plant_name)
            if not holding_company:
                return SimpleSearchResult(
                    power_plant_name=power_plant_name,
                    holding_company="",
                    financial_annual_links=[],
                    success=False,
                    error_message=f"Could not find holding company for {power_plant_name}"
                )
            
            print(f"🏢 Found holding company: {holding_company}")
            
            # Step 2: Find official website
            website = self._find_official_website(holding_company)
            if not website:
                return SimpleSearchResult(
                    power_plant_name=power_plant_name,
                    holding_company=holding_company,
                    financial_annual_links=[],
                    success=False,
                    error_message=f"Could not find official website for {holding_company}"
                )
            
            print(f"🌐 Found website: {website}")
            
            # Step 3: Find ALL links containing 'financial' or 'annual'
            financial_annual_links = self._find_financial_annual_links(website)
            
            if not financial_annual_links:
                return SimpleSearchResult(
                    power_plant_name=power_plant_name,
                    holding_company=holding_company,
                    financial_annual_links=[],
                    success=False,
                    error_message=f"Could not find any links containing 'financial' or 'annual' on {website}"
                )
            
            print(f"📄 Found {len(financial_annual_links)} financial/annual links")
            
            return SimpleSearchResult(
                power_plant_name=power_plant_name,
                holding_company=holding_company,
                financial_annual_links=financial_annual_links,
                success=True
            )
            
        except Exception as e:
            return SimpleSearchResult(
                power_plant_name=power_plant_name,
                holding_company="",
                financial_annual_links=[],
                success=False,
                error_message=f"Search failed: {str(e)}"
            )

    def _find_holding_company(self, power_plant_name: str) -> Optional[str]:
        """Find the holding company for a power plant"""
        try:
            search_prompt = f"""
            Search for "{power_plant_name} power plant holding company" and identify the parent company.
            
            Please respond with ONLY the holding company name, nothing else.
            If you cannot find a clear holding company, respond with "UNKNOWN".
            """
            
            response = self.genai_client.models.generate_content(
                model="gemini-2.0-flash-exp",
                contents=search_prompt,
                config={
                    "tools": [{"google_search": {}}],
                    "temperature": 0.1,
                }
            )
            
            holding_company = response.text.strip()
            
            if holding_company.upper() == "UNKNOWN" or not holding_company:
                return None
                
            return holding_company
            
        except Exception as e:
            print(f"Error finding holding company: {e}")
            return None

    def _find_official_website(self, company_name: str) -> Optional[str]:
        """Find the official website of a company"""
        try:
            search_prompt = f"""
            Search for the official website of "{company_name}".
            
            Please respond with ONLY the main website URL, nothing else.
            If you cannot find a clear official website, respond with "UNKNOWN".
            """
            
            response = self.genai_client.models.generate_content(
                model="gemini-2.0-flash-exp",
                contents=search_prompt,
                config={
                    "tools": [{"google_search": {}}],
                    "temperature": 0.1,
                }
            )
            
            website_text = response.text.strip()
            
            if website_text.upper() != "UNKNOWN" and website_text:
                # Extract URL from the response text
                website = self._extract_url_from_text(website_text)
                return website
            
            return None
                
        except Exception as e:
            print(f"Error finding official website: {e}")
            return None

    def _extract_url_from_text(self, text: str) -> Optional[str]:
        """Extract a valid URL from text response"""
        import re
        
        # Look for URLs in the text
        url_pattern = r'https?://[^\s\)\]\}]+'
        urls = re.findall(url_pattern, text)
        
        for url in urls:
            # Clean up the URL (remove trailing punctuation)
            url = re.sub(r'[,\.\)\]\}]+$', '', url)
            
            # Skip Google redirect URLs and other non-company URLs
            if any(skip in url.lower() for skip in ['google.com', 'redirect', 'vertexai', 'grounding-api']):
                continue
            
            return url
        
        # If no URLs found, check if the text itself looks like a domain
        text_clean = text.strip().lower()
        if '.' in text_clean and ' ' not in text_clean and len(text_clean) < 100:
            if not text_clean.startswith(('http://', 'https://')):
                return 'https://' + text_clean
            return text_clean
        
        return None

    def _find_financial_annual_links(self, website_url: str) -> List[str]:
        """
        Find ALL links containing 'financial' or 'annual' anywhere in the URL
        This is the core of the user's request
        """
        financial_annual_links = []
        
        try:
            base_url = f"{urlparse(website_url).scheme}://{urlparse(website_url).netloc}"
            
            print(f"🔍 Scanning website for financial/annual links: {website_url}")
            
            # Scan the main page
            response = requests.get(website_url, headers=self.headers, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find ALL links on the page
            for link in soup.find_all('a', href=True):
                href = link.get('href')
                if href:
                    full_url = urljoin(base_url, href)
                    full_url_lower = full_url.lower()
                    
                    # Check if URL contains 'financial' or 'annual' ANYWHERE
                    if 'financial' in full_url_lower or 'annual' in full_url_lower:
                        if full_url not in financial_annual_links:
                            financial_annual_links.append(full_url)
                            text = link.get_text().strip()[:50]
                            print(f"📄 Found: {text}... -> {full_url}")
            
            # Remove duplicates while preserving order
            unique_links = list(dict.fromkeys(financial_annual_links))
            
            print(f"✅ Total financial/annual links found: {len(unique_links)}")
            return unique_links
            
        except Exception as e:
            print(f"Error scanning website: {e}")
            return []


# CLI interface
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Simple Financial/Annual Link Search")
    parser.add_argument("power_plant_name", help="Name of the power plant")
    args = parser.parse_args()
    
    # Check for API key
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ Error: GEMINI_API_KEY environment variable is not set")
        exit(1)
    
    try:
        search_engine = SimpleFinancialAnnualSearchEngine()
        result = search_engine.search_financial_annual_links(args.power_plant_name)
        
        print(f"\n🏭 **Power Plant:** {result.power_plant_name}")
        
        if result.success:
            print(f"🏢 **Holding Company:** {result.holding_company}")
            print(f"✅ **Search Status:** Successful")
            print(f"📄 **Financial/Annual Links Found:** {len(result.financial_annual_links)}")
            
            if result.financial_annual_links:
                print(f"\n📋 **Links containing 'financial' or 'annual':**")
                for i, link in enumerate(result.financial_annual_links, 1):
                    print(f"  {i}. {link}")
                
                print(f"\n📝 **Summary:**")
                print(f"Found {len(result.financial_annual_links)} link(s) containing 'financial' or 'annual'")
                print("These links may contain the financial or annual reports you're looking for.")
        else:
            print(f"❌ **Search Status:** Failed")
            print(f"💬 **Error:** {result.error_message}")
        
        print("\n" + "=" * 60)
        
    except Exception as e:
        print(f"❌ Error: {e}")
