#!/usr/bin/env python3
"""
Dedicated Power Plant Search CLI
A simple command-line interface for the dedicated power plant search engine.

Usage:
    python dedicated_power_plant_cli.py "Vogtle Nuclear Plant"
    python dedicated_power_plant_cli.py "Three Mile Island" --years 3
"""

import argparse
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from agent.dedicated_power_plant_search import DedicatedPowerPlantSearchEngine


def main():
    """Run the dedicated power plant search from the command line."""
    parser = argparse.ArgumentParser(
        description="Search for power plant annual report PDFs",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python dedicated_power_plant_cli.py "Vogtle Nuclear Plant"
  python dedicated_power_plant_cli.py "Three Mile Island" --years 3
  python dedicated_power_plant_cli.py "Seil Power Plant" --years 5
        """
    )
    
    parser.add_argument(
        "power_plant_name", 
        help="Name of the power plant to search for"
    )
    parser.add_argument(
        "--years",
        type=int,
        default=5,
        help="Number of past years to search for annual reports (default: 5)"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Show detailed search progress"
    )
    
    args = parser.parse_args()
    
    # Check for API key
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ Error: GEMINI_API_KEY environment variable is not set")
        print("Please set your Gemini API key in the .env file or as an environment variable")
        sys.exit(1)
    
    try:
        # Initialize the search engine
        search_engine = DedicatedPowerPlantSearchEngine()
        
        print(f"🔍 Searching for annual reports for: {args.power_plant_name}")
        print(f"📅 Looking for reports from the past {args.years} years")
        print("=" * 60)
        
        # Perform the search
        result = search_engine.search_power_plant_pdfs(args.power_plant_name, args.years)
        
        # Display results
        print(f"\n🏭 **Power Plant:** {result.power_plant_name}")
        
        if result.success:
            print(f"🏢 **Holding Company:** {result.holding_company}")
            print(f"✅ **Search Status:** Successful")
            print(f"📄 **Annual Reports Found:** {len(result.pdf_links)}")
            
            if result.pdf_links:
                print(f"\n📋 **PDF Download Links:**")
                for i, pdf_link in enumerate(result.pdf_links, 1):
                    print(f"  {i}. {pdf_link}")
                
                print(f"\n📝 **Summary:**")
                print(f"Found {len(result.pdf_links)} annual report PDF(s) for {result.holding_company}")
                print("You can copy and paste these links to download the PDFs directly.")
            else:
                print("\n⚠️  No PDF links found, but search completed successfully.")
        else:
            print(f"❌ **Search Status:** Failed")
            print(f"🔍 **Holding Company:** {result.holding_company if result.holding_company else 'Not found'}")
            print(f"💬 **Error:** {result.error_message}")
            
            print(f"\n💡 **Suggestions:**")
            if result.holding_company:
                print(f"• Try searching for '{result.holding_company}' directly on their website")
                print(f"• Check the investor relations section manually")
            print(f"• Verify the power plant name spelling")
            print(f"• Some companies may not publish annual reports publicly")
            print(f"• International companies may have different reporting structures")
        
        print("\n" + "=" * 60)
        print("Search completed!")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  Search interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
