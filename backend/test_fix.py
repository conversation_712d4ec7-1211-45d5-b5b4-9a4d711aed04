#!/usr/bin/env python3
"""Test the fix for power plant search"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from langchain_core.messages import HumanMessage
from agent.state import OverallState
from agent.graph import graph

def test_power_plant_fix():
    """Test the power plant search fix"""
    
    # Test message
    test_message = HumanMessage(content="Vogtle Nuclear Plant")
    
    # Create initial state
    initial_state = OverallState(
        messages=[test_message]
    )
    
    # Create configuration
    config = {
        "configurable": {
            "query_generator_model": "gemini-2.0-flash-exp",
            "reflection_model": "gemini-2.0-flash-exp",
            "answer_model": "gemini-2.0-flash-exp",
            "number_of_initial_queries": 3,
            "max_research_loops": 3
        }
    }
    
    try:
        print("Testing power plant search fix...")
        
        # Run the graph
        result = graph.invoke(initial_state, config)
        
        print("Graph execution completed!")
        
        # Check if we got a response
        if result.get("messages"):
            final_message = result["messages"][-1]
            print(f"Final response: {final_message.content[:300]}...")
            
            # Check if it contains expected power plant info
            if "Southern Company" in final_message.content:
                print("✅ Successfully found holding company!")
            
            if "pdf" in final_message.content.lower():
                print("✅ Successfully found PDF links!")
                
            if "Search Successful" in final_message.content:
                print("✅ Search reported as successful!")
                
        else:
            print("❌ No response received")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_power_plant_fix()