#!/usr/bin/env python3
"""
Simple Power Plant Search - Standalone Version
No complex dependencies, just basic search functionality for power plants.
"""

import os
import sys
import argparse

def main():
    """Simple power plant search without complex dependencies"""
    parser = argparse.ArgumentParser(description="Search for power plant annual reports")
    parser.add_argument("power_plant_name", help="Name of the power plant")
    parser.add_argument("--years", type=int, default=5, help="Years to search (default: 5)")
    
    args = parser.parse_args()
    
    print(f"🔍 Searching for: {args.power_plant_name}")
    print(f"📅 Looking for reports from the past {args.years} years")
    print("=" * 60)
    
    # Check for API key
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        print("❌ Error: GEMINI_API_KEY environment variable is not set")
        print("Please set your Gemini API key in the .env file or as an environment variable")
        print("\nTo set it temporarily:")
        print("export GEMINI_API_KEY='your_api_key_here'")
        return
    
    try:
        # Try to import and use the search engine
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
        from agent.dedicated_power_plant_search import DedicatedPowerPlantSearchEngine
        
        search_engine = DedicatedPowerPlantSearchEngine()
        result = search_engine.search_power_plant_pdfs(args.power_plant_name, args.years)
        
        # Display results
        print(f"\n🏭 **Power Plant:** {result.power_plant_name}")
        
        if result.success:
            print(f"🏢 **Holding Company:** {result.holding_company}")
            print(f"✅ **Search Status:** Successful")
            print(f"📄 **Annual Reports Found:** {len(result.pdf_links)}")
            
            if result.pdf_links:
                print(f"\n📋 **PDF Download Links:**")
                for i, pdf_link in enumerate(result.pdf_links, 1):
                    print(f"  {i}. {pdf_link}")
                
                print(f"\n📝 **Summary:**")
                print(f"Found {len(result.pdf_links)} annual report PDF(s)")
                print("You can copy and paste these links to download the PDFs directly.")
        else:
            print(f"❌ **Search Status:** Failed")
            print(f"💬 **Error:** {result.error_message}")
            
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("\n🔧 **Setup Required:**")
        print("Please install the required dependencies:")
        print("pip install pydantic langgraph langchain langchain-google-genai python-dotenv google-genai requests beautifulsoup4 lxml")
        print("\nOr run the setup script:")
        print("pip install -e .")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
