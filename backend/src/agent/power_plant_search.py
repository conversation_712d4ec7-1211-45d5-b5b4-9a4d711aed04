"""
Power Plant Search Engine
A specialized search engine for finding power plant holding companies and their annual reports.
"""

import os
import re
import requests
from typing import List, Dict, Optional, Any
from dataclasses import dataclass
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import time
from google.genai import Client
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage
from dotenv import load_dotenv

load_dotenv()

@dataclass
class PowerPlantInfo:
    """Information about a power plant"""
    name: str
    holding_company: str
    confidence_score: float = 0.0

@dataclass
class AnnualReport:
    """Annual report information"""
    year: int
    title: str
    pdf_url: str
    file_size: Optional[str] = None
    company_name: str = ""

class PowerPlantSearchEngine:
    """
    Specialized search engine for power plant holding companies and annual reports
    """
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.getenv("GEMINI_API_KEY")
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY is required")
        
        self.genai_client = Client(api_key=self.api_key)
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash-exp",
            temperature=0.3,
            max_retries=2,
            api_key=self.api_key,
        )
        
        # Headers for web scraping
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        # Common financial sections keywords (expanded for international companies)
        self.financial_keywords = [
            # Standard English terms
            'investor', 'investors', 'financial', 'financials', 'annual report', 
            'annual reports', 'sec filing', 'sec filings', 'investor relations',
            'financial statements', 'financial information', 'financial data',
            # International and alternative terms
            'corporate governance', 'governance', 'publications', 'reports',
            'sustainability report', 'company report', 'financial report',
            'corporate information', 'company information', 'downloads',
            'financial results', 'earnings', 'quarterly report', 'fiscal year',
            'board report', 'management report', 'disclosure', 'transparency'
        ]
        
        # File extensions to look for
        self.pdf_extensions = ['.pdf', '.PDF']
        
    def search_power_plant_holding_company(self, power_plant_name: str) -> Optional[PowerPlantInfo]:
        """
        Search for the holding company of a power plant
        
        Args:
            power_plant_name: Name of the power plant
            
        Returns:
            PowerPlantInfo object with holding company information
        """
        try:
            # Construct search query
            search_query = f"{power_plant_name} power plant holding company owner parent company"
            
            # Use Gemini with Google Search to find holding company
            response = self.genai_client.models.generate_content(
                model="gemini-2.0-flash-exp",
                contents=f"""
                Search for information about "{power_plant_name}" power plant and identify its holding company or parent company.
                
                Please provide:
                1. The exact name of the holding company or parent company
                2. A confidence score (0.0-1.0) based on how certain you are about this information
                
                Search Query: {search_query}
                """,
                config={
                    "tools": [{"google_search": {}}],
                    "temperature": 0.3,
                }
            )
            
            # Extract holding company information using LLM
            extraction_prompt = f"""
            Based on the following search results about "{power_plant_name}" power plant, 
            extract the holding company or parent company name.
            
            Search Results:
            {response.text}
            
            Please respond with ONLY the holding company name, nothing else.
            If you cannot find a clear holding company, respond with "UNKNOWN".
            """
            
            extraction_response = self.llm.invoke([HumanMessage(content=extraction_prompt)])
            holding_company = extraction_response.content.strip()
            
            if holding_company.upper() == "UNKNOWN":
                return None
                
            return PowerPlantInfo(
                name=power_plant_name,
                holding_company=holding_company,
                confidence_score=0.8  # Default confidence score
            )
            
        except Exception as e:
            print(f"Error searching for holding company: {e}")
            return None
    
    def search_annual_reports(self, holding_company_name: str, years: int = 5) -> List[AnnualReport]:
        """
        Search for annual reports of a holding company
        
        Args:
            holding_company_name: Name of the holding company
            years: Number of past years to search for (default: 5)
            
        Returns:
            List of AnnualReport objects
        """
        reports = []
        
        try:
            # Step 1: Find company's official website
            official_website = self._find_official_website(holding_company_name)
            if not official_website:
                return reports
            
            # Step 2: Search for investor relations or financial sections
            financial_urls = self._find_financial_sections(official_website)
            
            # Step 3: Extract PDF links from financial sections
            for url in financial_urls:
                try:
                    # Check if the URL itself is a PDF
                    if url.lower().endswith('.pdf'):
                        # Extract year from URL
                        year = self._extract_year_from_text(url)
                        if year and year >= (2024 - years):
                            # Check if it's likely an annual report
                            if self._is_annual_report_link("", url):
                                report = AnnualReport(
                                    year=year,
                                    title=f"Annual Report {year}",
                                    pdf_url=url,
                                    company_name=holding_company_name
                                )
                                reports.append(report)
                    else:
                        # Extract from page
                        page_reports = self._extract_pdf_links_from_page(url, holding_company_name, years)
                        if page_reports:
                            reports.extend(page_reports)
                    time.sleep(1)  # Be respectful to the server
                except Exception as e:
                    print(f"Error extracting PDFs from {url}: {e}")
                    continue
            
            # Step 4: Deduplicate and sort reports
            reports = self._deduplicate_reports(reports)
            reports.sort(key=lambda x: x.year, reverse=True)
            
            return reports[:years]  # Return only the requested number of years
            
        except Exception as e:
            print(f"Error searching for annual reports: {e}")
            return reports
    
    def _find_official_website(self, company_name: str) -> Optional[str]:
        """Find the official website of a company"""
        try:
            # Use a more direct approach for well-known companies
            known_companies = {
                "Southern Company": "https://www.southerncompany.com",
                "Duke Energy": "https://www.duke-energy.com",
                "Exelon Corporation": "https://www.exeloncorp.com",
                "NextEra Energy": "https://www.nexteraenergy.com",
                "Entergy Corporation": "https://www.entergy.com",
                "Pacific Gas and Electric": "https://www.pge.com",
                "Edison International": "https://www.edison.com",
                "Dominion Energy": "https://www.dominionenergy.com",
                "American Electric Power": "https://www.aep.com",
                "Xcel Energy": "https://www.xcelenergy.com"
            }
            
            # Check if it's a known company first
            for known_name, url in known_companies.items():
                if known_name.lower() in company_name.lower() or company_name.lower() in known_name.lower():
                    return url
            
            # If not found in known companies, use search
            search_query = f"{company_name} official website"
            
            response = self.genai_client.models.generate_content(
                model="gemini-2.0-flash-exp",
                contents=f"""
                Search for the official website of "{company_name}".
                
                Please provide ONLY the main website URL (like https://example.com), nothing else.
                If you cannot find the official website, respond with "UNKNOWN".
                
                Search Query: {search_query}
                """,
                config={
                    "tools": [{"google_search": {}}],
                    "temperature": 0.1,
                }
            )
            
            # Extract URL from response
            extraction_prompt = f"""
            From the following search results, extract the official website URL for "{company_name}".
            
            Search Results:
            {response.text}
            
            Please respond with ONLY the main website URL (like https://example.com), nothing else.
            If you cannot find a clear official website, respond with "UNKNOWN".
            """
            
            extraction_response = self.llm.invoke([HumanMessage(content=extraction_prompt)])
            website = extraction_response.content.strip()
            
            if website.upper() == "UNKNOWN":
                return None
                
            # Validate URL format
            if not website.startswith(('http://', 'https://')):
                website = 'https://' + website
                
            return website
            
        except Exception as e:
            print(f"Error finding official website: {e}")
            return None
    
    def _find_financial_sections(self, website_url: str) -> List[str]:
        """Find financial/investor relations sections on a website"""
        financial_urls = []
        
        try:
            base_url = f"{urlparse(website_url).scheme}://{urlparse(website_url).netloc}"
            
            # First try common paths directly for various company types
            common_paths = [
                # US/Western style
                '/investors', '/investor-relations', '/investor_relations',
                '/financial-information', '/annual-reports', '/sec-filings',
                '/financials', '/reports', '/annual_reports', '/investor',
                '/corporate/investor-relations', '/about/investor-relations',
                '/sustainability/reports', '/investors/sec-filings',
                # International/GCC style
                '/about-us', '/about', '/corporate', '/governance',
                '/corporate-governance', '/financial-reports', '/publications',
                '/media-center', '/news-and-reports', '/downloads',
                '/corporate-information', '/company-information',
                # Generic document repositories
                '/documents', '/files', '/resources', '/library'
            ]
            
            for path in common_paths:
                test_url = urljoin(base_url, path)
                try:
                    test_response = requests.head(test_url, headers=self.headers, timeout=5)
                    if test_response.status_code == 200:
                        financial_urls.append(test_url)
                except Exception as e:
                    continue
            
            # If we found some paths, use those
            if financial_urls:
                return financial_urls
            
            # Otherwise, try to scrape the main page
            try:
                response = requests.get(website_url, headers=self.headers, timeout=10)
                response.raise_for_status()
                
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Find links that might lead to financial sections
                for link in soup.find_all('a', href=True):
                    href = link.get('href')
                    text = link.get_text().lower()
                    
                    # Check if link text contains financial keywords
                    if any(keyword in text for keyword in self.financial_keywords):
                        full_url = urljoin(base_url, href)
                        if full_url not in financial_urls:
                            financial_urls.append(full_url)
                            
            except Exception as e:
                print(f"Error scraping main page: {e}")
                        
        except Exception as e:
            print(f"Error finding financial sections: {e}")
            
        return financial_urls
    
    def _extract_pdf_links_from_page(self, url: str, company_name: str, years: int) -> List[AnnualReport]:
        """Extract PDF links from a financial page"""
        reports = []
        
        try:
            response = requests.get(url, headers=self.headers, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            base_url = f"{urlparse(url).scheme}://{urlparse(url).netloc}"
            
            # Find all PDF links
            for link in soup.find_all('a', href=True):
                href = link.get('href')
                text = link.get_text().strip()
                
                # Check if it's a PDF link
                if any(href.lower().endswith(ext.lower()) for ext in self.pdf_extensions):
                    full_url = urljoin(base_url, href)
                    
                    # Check if it's likely an annual report
                    if self._is_annual_report_link(text, href):
                        year = self._extract_year_from_text(text + " " + href)
                        if year and year >= (2024 - years):  # Only get reports from recent years
                            reports.append(AnnualReport(
                                year=year,
                                title=text or f"Annual Report {year}",
                                pdf_url=full_url,
                                company_name=company_name
                            ))
                    else:
                        # Even if not specifically annual report, check for recent years
                        year = self._extract_year_from_text(text + " " + href)
                        if year and year >= (2024 - years):
                            # Check if it contains any report-related keywords
                            combined_text = (text + " " + href).lower()
                            if any(keyword in combined_text for keyword in ['report', 'annual', 'filing', '10-k']):
                                reports.append(AnnualReport(
                                    year=year,
                                    title=text or f"Report {year}",
                                    pdf_url=full_url,
                                    company_name=company_name
                                ))
                            
        except Exception as e:
            print(f"Error extracting PDF links: {e}")
            
        return reports
    
    def _is_annual_report_link(self, text: str, href: str) -> bool:
        """Check if a link is likely an annual report"""
        combined_text = (text + " " + href).lower()
        
        # Keywords that indicate annual reports
        annual_report_keywords = [
            'annual report', 'annual-report', 'annual_report', 'annual', 
            '10-k', 'form 10-k', 'yearly report', 'annual filing',
            'ar/', '/ar/', '2024-annual', '2023-annual', '2022-annual',
            '2021-annual', '2020-annual'
        ]
        
        # Keywords to exclude (financial statements are separate)
        exclude_keywords = [
            'quarterly', 'q1 ', 'q2 ', 'q3 ', 'q4 ', 'interim',
            'proxy', 'financial statement', 'cash flow', 'balance sheet',
            ' q1', ' q2', ' q3', ' q4'
        ]
        
        # Check for annual report keywords
        has_annual_keywords = any(keyword in combined_text for keyword in annual_report_keywords)
        
        # Check for exclude keywords
        has_exclude_keywords = any(keyword in combined_text for keyword in exclude_keywords)
        
        return has_annual_keywords and not has_exclude_keywords
    
    def _extract_year_from_text(self, text: str) -> Optional[int]:
        """Extract year from text"""
        # Look for 4-digit years (2020-2024)
        year_pattern = r'\b(20[2-9][0-9])\b'
        matches = re.findall(year_pattern, text)
        
        if matches:
            return int(matches[-1])  # Return the last (most recent) year found
        
        return None
    
    def _deduplicate_reports(self, reports: List[AnnualReport]) -> List[AnnualReport]:
        """Remove duplicate reports based on year and URL"""
        seen = set()
        deduplicated = []
        
        for report in reports:
            key = (report.year, report.pdf_url)
            if key not in seen:
                seen.add(key)
                deduplicated.append(report)
                
        return deduplicated
    
    def search_power_plant_annual_reports(self, power_plant_name: str, years: int = 5) -> Dict[str, Any]:
        """
        Complete search pipeline for power plant annual reports
        
        Args:
            power_plant_name: Name of the power plant
            years: Number of past years to search for (default: 5)
            
        Returns:
            Dictionary containing search results
        """
        result = {
            "power_plant_name": power_plant_name,
            "holding_company": None,
            "annual_reports": [],
            "pdf_links": [],
            "search_successful": False,
            "error_message": None
        }
        
        try:
            # Step 1: Find holding company
            print(f"Searching for holding company of {power_plant_name}...")
            power_plant_info = self.search_power_plant_holding_company(power_plant_name)
            
            if not power_plant_info:
                result["error_message"] = f"Could not find holding company for {power_plant_name}"
                return result
            
            result["holding_company"] = power_plant_info.holding_company
            
            # Step 2: Search for annual reports
            annual_reports = self.search_annual_reports(power_plant_info.holding_company, years)
            
            if not annual_reports:
                result["error_message"] = f"Could not find annual reports for {power_plant_info.holding_company}"
                return result
            
            result["annual_reports"] = annual_reports
            result["pdf_links"] = [report.pdf_url for report in annual_reports]
            result["search_successful"] = True
            
        except Exception as e:
            result["error_message"] = f"Search failed: {str(e)}"
            
        return result

# Example usage and testing
if __name__ == "__main__":
    # Example usage
    search_engine = PowerPlantSearchEngine()
    
    # Test with a power plant name
    test_power_plant = "Vogtle Nuclear Plant"
    
    result = search_engine.search_power_plant_annual_reports(test_power_plant, years=3)
    
    print(f"\nResults for {test_power_plant}:")
    print(f"Holding Company: {result['holding_company']}")
    print(f"Search Successful: {result['search_successful']}")
    
    if result['search_successful']:
        print(f"Found {len(result['annual_reports'])} annual reports:")
        for report in result['annual_reports']:
            print(f"  - {report.year}: {report.title}")
            print(f"    PDF: {report.pdf_url}")
    else:
        print(f"Error: {result['error_message']}")