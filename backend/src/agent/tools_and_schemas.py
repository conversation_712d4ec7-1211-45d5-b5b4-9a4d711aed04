from typing import List, Optional
from pydantic import BaseModel, Field


class SearchQueryList(BaseModel):
    query: List[str] = Field(
        description="A list of search queries to be used for web research."
    )
    rationale: str = Field(
        description="A brief explanation of why these queries are relevant to the research topic."
    )


class Reflection(BaseModel):
    is_sufficient: bool = Field(
        description="Whether the provided summaries are sufficient to answer the user's question."
    )
    knowledge_gap: str = Field(
        description="A description of what information is missing or needs clarification."
    )
    follow_up_queries: List[str] = Field(
        description="A list of follow-up queries to address the knowledge gap."
    )


class PowerPlantSearchRequest(BaseModel):
    power_plant_name: str = Field(
        description="Name of the power plant to search for"
    )
    years: int = Field(
        default=5,
        description="Number of past years to search for annual reports (default: 5)"
    )


class PowerPlantHoldingCompany(BaseModel):
    name: str = Field(
        description="Name of the power plant"
    )
    holding_company: str = Field(
        description="Name of the holding company or parent company"
    )
    confidence_score: float = Field(
        description="Confidence score for the holding company identification (0.0-1.0)"
    )


class AnnualReportInfo(BaseModel):
    year: int = Field(
        description="Year of the annual report"
    )
    title: str = Field(
        description="Title of the annual report"
    )
    pdf_url: str = Field(
        description="Direct PDF download URL"
    )
    file_size: Optional[str] = Field(
        default=None,
        description="File size of the PDF (if available)"
    )
    company_name: str = Field(
        description="Name of the company that published the report"
    )


class PowerPlantSearchResult(BaseModel):
    power_plant_name: str = Field(
        description="Name of the power plant that was searched"
    )
    holding_company: Optional[str] = Field(
        default=None,
        description="Name of the holding company (if found)"
    )
    annual_reports: List[AnnualReportInfo] = Field(
        description="List of annual reports found"
    )
    pdf_links: List[str] = Field(
        description="List of direct PDF download URLs"
    )
    search_successful: bool = Field(
        description="Whether the search was successful"
    )
    error_message: Optional[str] = Field(
        default=None,
        description="Error message if search failed"
    )
