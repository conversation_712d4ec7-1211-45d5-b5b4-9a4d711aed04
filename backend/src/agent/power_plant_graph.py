"""
Power Plant Search Graph
A specialized LangGraph implementation for power plant holding company and annual report searches.
"""

import os
from typing import Dict, Any, List

from agent.tools_and_schemas import (
    PowerPlantSearchRequest, 
    PowerPlantSearchResult,
    AnnualReportInfo
)
from agent.power_plant_search import PowerPlantSearchEngine
from dotenv import load_dotenv
from langchain_core.messages import AIMessage, HumanMessage
from langgraph.graph import StateGraph, START, END
from langgraph.graph.state import CompiledStateGraph
from langgraph.types import Send
from langchain_core.runnables import RunnableConfig
from agent.configuration import Configuration
from agent.state import OverallState
from pydantic import BaseModel, Field

load_dotenv()


class PowerPlantSearchState(BaseModel):
    """State for power plant search operations"""
    power_plant_name: str = Field(description="Name of the power plant to search for")
    years: int = Field(default=5, description="Number of years of reports to search for")
    holding_company: str = Field(default="", description="Found holding company name")
    annual_reports: List[Dict[str, Any]] = Field(default=[], description="List of found annual reports")
    pdf_links: List[str] = Field(default=[], description="List of PDF download URLs")
    search_successful: bool = Field(default=False, description="Whether search was successful")
    error_message: str = Field(default="", description="Error message if search failed")
    messages: List[Any] = Field(default=[], description="Chat messages")


def extract_power_plant_name(state: OverallState, config: RunnableConfig) -> OverallState:
    """
    Extract power plant name from user message
    
    Args:
        state: Current graph state containing the user's message
        config: Configuration for the runnable
        
    Returns:
        OverallState with extracted power plant name
    """
    try:
        # Get the latest user message
        user_message = ""
        if state.get("messages"):
            for msg in reversed(state["messages"]):
                if hasattr(msg, 'type') and msg.type == "human":
                    user_message = msg.content
                    break
        
        # Simple extraction - assume the entire message is the power plant name
        # In a more sophisticated implementation, you could use NLP to extract the plant name
        power_plant_name = user_message.strip()
        
        # Remove common prefixes/suffixes that might be added
        prefixes_to_remove = [
            "search for", "find", "look up", "get reports for", 
            "annual reports for", "find annual reports for"
        ]
        
        for prefix in prefixes_to_remove:
            if power_plant_name.lower().startswith(prefix.lower()):
                power_plant_name = power_plant_name[len(prefix):].strip()
        
        # Store power plant info in the state
        return {
            **state,
            "power_plant_name": power_plant_name,
            "years": 5,
            "search_step": "extract_power_plant_name"
        }
        
    except Exception as e:
        return {
            **state,
            "power_plant_name": "",
            "years": 5,
            "error_message": f"Failed to extract power plant name: {str(e)}",
            "search_step": "error"
        }


def search_holding_company(state: OverallState, config: RunnableConfig) -> OverallState:
    """
    Search for the holding company of the power plant
    
    Args:
        state: Current graph state with power plant name
        config: Configuration for the runnable
        
    Returns:
        Updated state with holding company information
    """
    try:
        power_plant_name = state.get("power_plant_name")
        if not power_plant_name:
            return {
                **state,
                "error_message": "No power plant name provided",
                "search_step": "error"
            }
        
        # Initialize search engine
        search_engine = PowerPlantSearchEngine()
        
        # Search for holding company
        power_plant_info = search_engine.search_power_plant_holding_company(power_plant_name)
        
        if not power_plant_info:
            return {
                **state,
                "error_message": f"Could not find holding company for {power_plant_name}",
                "search_step": "error"
            }
        
        return {
            **state,
            "holding_company": power_plant_info.holding_company,
            "search_step": "search_holding_company"
        }
        
    except Exception as e:
        return {
            **state,
            "error_message": f"Error searching for holding company: {str(e)}",
            "search_step": "error"
        }


def search_annual_reports(state: OverallState, config: RunnableConfig) -> OverallState:
    """
    Search for annual reports of the holding company
    
    Args:
        state: Current graph state with holding company information
        config: Configuration for the runnable
        
    Returns:
        Updated state with annual reports information
    """
    try:
        holding_company = state.get("holding_company")
        if not holding_company:
            return {
                **state,
                "error_message": "No holding company found to search for reports",
                "search_step": "error"
            }
        
        # Initialize search engine
        search_engine = PowerPlantSearchEngine()
        
        # Search for annual reports
        years = state.get("years", 5)
        annual_reports = search_engine.search_annual_reports(holding_company, years)
        
        if not annual_reports:
            return {
                **state,
                "error_message": f"Could not find annual reports for {holding_company}. This may be because the company's website doesn't have publicly accessible annual reports or uses a different structure than expected. Try searching for the company name directly or check their official website.",
                "search_step": "error"
            }
        
        # Convert to dictionaries for state storage
        reports_data = []
        pdf_links = []
        
        for report in annual_reports:
            report_dict = {
                "year": report.year,
                "title": report.title,
                "pdf_url": report.pdf_url,
                "file_size": report.file_size,
                "company_name": report.company_name
            }
            reports_data.append(report_dict)
            pdf_links.append(report.pdf_url)
        
        return {
            **state,
            "annual_reports": reports_data,
            "pdf_links": pdf_links,
            "search_successful": True,
            "search_step": "search_annual_reports"
        }
        
    except Exception as e:
        return {
            **state,
            "error_message": f"Error searching for annual reports: {str(e)}",
            "search_step": "error"
        }


def format_results(state: OverallState, config: RunnableConfig) -> OverallState:
    """
    Format the search results into a user-friendly response
    
    Args:
        state: Current graph state with search results
        config: Configuration for the runnable
        
    Returns:
        OverallState with formatted results message
    """
    try:
        search_successful = state.get("search_successful", False)
        power_plant_name = state.get("power_plant_name", "Unknown")
        
        if not search_successful:
            error_msg = state.get("error_message", "Search failed for unknown reason")
            holding_company = state.get("holding_company", "")
            
            response_content = f"""
**Power Plant Search Results**

❌ **Search Failed**
🏭 **Power Plant:** {power_plant_name}
"""
            if holding_company:
                response_content += f"🏢 **Holding Company Found:** {holding_company}\n"
            
            response_content += f"""
**Error:** {error_msg}

💡 **Suggestions:**
• Try searching for the holding company "{holding_company}" directly on their website
• Check if the company has a different name or structure
• Some companies may not publish annual reports publicly
• International companies may have different reporting requirements
"""
        else:
            # Format successful results
            holding_company = state.get("holding_company", "Unknown")
            annual_reports = state.get("annual_reports", [])
            pdf_links = state.get("pdf_links", [])
            
            response_content = f"""
**Power Plant Search Results**

✅ **Search Successful**

🏭 **Power Plant:** {power_plant_name}
🏢 **Holding Company:** {holding_company}
📊 **Annual Reports Found:** {len(annual_reports)} reports

**PDF Download Links:**
"""
            
            for i, report in enumerate(annual_reports, 1):
                response_content += f"""
{i}. **{report['title']}** ({report['year']})
   📄 PDF: {report['pdf_url']}
"""
            
            response_content += f"""
**Summary of PDF Links:**
"""
            for link in pdf_links:
                response_content += f"• {link}\n"
        
        return {
            **state,
            "messages": state.get("messages", []) + [AIMessage(content=response_content.strip())],
            "sources_gathered": [],
        }
        
    except Exception as e:
        error_response = f"""
**Power Plant Search Results**

❌ **Error formatting results**
Error: {str(e)}
"""
        return {
            **state,
            "messages": state.get("messages", []) + [AIMessage(content=error_response.strip())],
            "sources_gathered": [],
        }


def check_if_power_plant_search(state: OverallState) -> str:
    """
    Check if the user query is asking for power plant information
    
    Args:
        state: Current graph state
        
    Returns:
        Next node to execute
    """
    try:
        # Get the latest user message
        user_message = ""
        if state.get("messages"):
            for msg in reversed(state["messages"]):
                if hasattr(msg, 'type') and msg.type == "human":
                    user_message = msg.content.lower()
                    break
        
        # Keywords that indicate power plant search
        power_plant_keywords = [
            "power plant", "nuclear plant", "coal plant", "gas plant",
            "wind farm", "solar farm", "hydroelectric", "power station",
            "generating station", "annual report", "holding company"
        ]
        
        # Check if message contains power plant related keywords
        if any(keyword in user_message for keyword in power_plant_keywords):
            return "extract_power_plant_name"
        else:
            return "generate_query"  # Fall back to regular research
            
    except Exception as e:
        return "generate_query"  # Fall back to regular research on error


# Create the power plant search graph
def create_power_plant_graph() -> CompiledStateGraph:
    """Create and return the power plant search graph"""
    
    # Create a hybrid state graph that can handle both OverallState and PowerPlantSearchState
    builder = StateGraph(OverallState, config_schema=Configuration)
    
    # Add nodes
    builder.add_node("extract_power_plant_name", extract_power_plant_name)
    builder.add_node("search_holding_company", search_holding_company)
    builder.add_node("search_annual_reports", search_annual_reports)
    builder.add_node("format_results", format_results)
    
    # Add edges
    builder.add_edge("extract_power_plant_name", "search_holding_company")
    builder.add_edge("search_holding_company", "search_annual_reports")
    builder.add_edge("search_annual_reports", "format_results")
    builder.add_edge("format_results", END)
    
    return builder.compile(name="power-plant-search")


# For testing purposes
if __name__ == "__main__":
    # Test the power plant search functionality
    from agent.state import OverallState
    from langchain_core.messages import HumanMessage
    
    # Create test state
    test_state = OverallState(
        messages=[HumanMessage(content="Vogtle Nuclear Plant")]
    )
    
    # Test the search
    graph = create_power_plant_graph()
    
    # Run the graph (you would need to set up proper configuration)
    print("Power Plant Search Graph created successfully!")