"""
Dedicated Power Plant Search Engine
Optimized for the specific use case: Power Plant Name -> Holding Company -> Annual Report PDFs

This engine focuses on:
1. Taking power plant name as input
2. Finding the holding company using Google Search + Gemini
3. Finding the holding company's official website
4. Scraping investor relations/financial sections for annual report PDFs
5. Returning only PDF links for the past 5 years
"""

import os
import re
import requests
from typing import List, Dict, Optional, Any
from dataclasses import dataclass
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import time
from google.genai import Client
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.messages import HumanMessage
from dotenv import load_dotenv

load_dotenv()

@dataclass
class SearchResult:
    """Final search result containing only PDF links"""
    power_plant_name: str
    holding_company: str
    pdf_links: List[str]
    success: bool
    error_message: Optional[str] = None

class DedicatedPowerPlantSearchEngine:
    """
    Dedicated search engine for power plant annual report PDFs
    """
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.getenv("GEMINI_API_KEY")
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY is required")
        
        self.genai_client = Client(api_key=self.api_key)
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash-exp",
            temperature=0.1,
            max_retries=2,
            api_key=self.api_key,
        )
        
        # Headers for web scraping
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        # Keywords for finding financial sections
        self.financial_keywords = [
            'investor', 'investors', 'investor relations', 'financial', 'financials',
            'annual report', 'annual reports', 'sec filing', 'sec filings',
            'financial statements', 'financial information', 'corporate governance',
            'reports', 'publications', 'downloads', 'financial results'
        ]
        
        # Keywords specifically for annual reports (not financial statements)
        self.annual_report_keywords = [
            'annual report', 'annual-report', 'annual_report', 'yearly report',
            '10-k', 'form 10-k', 'annual filing', 'ar/', '/ar/'
        ]
        
        # Keywords to exclude (financial statements are separate from annual reports)
        self.exclude_keywords = [
            'quarterly', 'q1 ', 'q2 ', 'q3 ', 'q4 ', 'interim', 'proxy',
            'financial statement', 'cash flow', 'balance sheet', 'income statement'
        ]

    def search_power_plant_pdfs(self, power_plant_name: str, years: int = 5) -> SearchResult:
        """
        Main search function - takes power plant name and returns PDF links

        Strategy:
        1. First try direct search: "{power plant name} annual report"
        2. If no results, fall back to holding company search

        Args:
            power_plant_name: Name of the power plant
            years: Number of past years to search for (default: 5)

        Returns:
            SearchResult with PDF links or error message
        """
        try:
            print(f"🔍 Searching for: {power_plant_name}")

            # Step 1: Try direct search first
            print(f"📋 Step 1: Trying direct search for '{power_plant_name} annual report'")
            direct_pdf_links = self._direct_annual_report_search(power_plant_name, years)

            if direct_pdf_links:
                print(f"✅ Direct search successful! Found {len(direct_pdf_links)} PDF links")
                return SearchResult(
                    power_plant_name=power_plant_name,
                    holding_company="Direct search (no holding company needed)",
                    pdf_links=direct_pdf_links,
                    success=True
                )

            print(f"⚠️  Direct search found no results, trying holding company approach...")

            # Step 2: Fall back to holding company search
            print(f"📋 Step 2: Searching for holding company")
            holding_company = self._find_holding_company(power_plant_name)
            if not holding_company:
                return SearchResult(
                    power_plant_name=power_plant_name,
                    holding_company="",
                    pdf_links=[],
                    success=False,
                    error_message=f"Could not find annual reports directly or identify holding company for {power_plant_name}"
                )

            print(f"🏢 Found holding company: {holding_company}")

            # Step 3: Find official website
            website = self._find_official_website(holding_company)
            if not website:
                return SearchResult(
                    power_plant_name=power_plant_name,
                    holding_company=holding_company,
                    pdf_links=[],
                    success=False,
                    error_message=f"Could not find official website for {holding_company}"
                )

            print(f"🌐 Found website: {website}")

            # Step 4: Find financial sections
            financial_urls = self._find_financial_sections(website)
            if not financial_urls:
                return SearchResult(
                    power_plant_name=power_plant_name,
                    holding_company=holding_company,
                    pdf_links=[],
                    success=False,
                    error_message=f"Could not find investor relations or financial sections on {website}"
                )

            print(f"📊 Found {len(financial_urls)} financial sections")

            # Step 5: Extract PDF links
            pdf_links = self._extract_annual_report_pdfs(financial_urls, years)

            if not pdf_links:
                return SearchResult(
                    power_plant_name=power_plant_name,
                    holding_company=holding_company,
                    pdf_links=[],
                    success=False,
                    error_message=f"Could not find annual report PDFs for {holding_company}. The company may not publish annual reports publicly or may use a different structure."
                )

            print(f"📄 Found {len(pdf_links)} PDF links")

            return SearchResult(
                power_plant_name=power_plant_name,
                holding_company=holding_company,
                pdf_links=pdf_links,
                success=True
            )

        except Exception as e:
            return SearchResult(
                power_plant_name=power_plant_name,
                holding_company="",
                pdf_links=[],
                success=False,
                error_message=f"Search failed: {str(e)}"
            )

    def _direct_annual_report_search(self, power_plant_name: str, years: int) -> List[str]:
        """
        Try direct search for annual reports using "{power plant name} annual report"

        Args:
            power_plant_name: Name of the power plant
            years: Number of past years to search for

        Returns:
            List of PDF links found directly, empty list if none found
        """
        try:
            search_prompt = f"""
            Search for "{power_plant_name} annual report" and look for direct PDF download links.

            Focus on finding:
            - Annual report PDFs specifically for {power_plant_name}
            - Direct PDF download links (ending in .pdf)
            - Recent reports from the past {years} years (2020-2024)

            Look for search results that contain:
            - PDF files with "annual report" in the title or URL
            - Official company documents
            - Investor relations materials

            If you find direct PDF links, list them one per line starting with "FOUND:".
            If you don't find any direct PDF links, respond with "NO_DIRECT_LINKS".

            Example response if PDFs found:
            FOUND: https://example.com/annual-report-2023.pdf
            FOUND: https://example.com/2022-annual-report.pdf

            Example if no direct PDFs found:
            NO_DIRECT_LINKS
            """

            response = self.genai_client.models.generate_content(
                model="gemini-2.0-flash-exp",
                contents=search_prompt,
                config={
                    "tools": [{"google_search": {}}],
                    "temperature": 0.1,
                }
            )

            response_text = response.text.strip()

            if "NO_DIRECT_LINKS" in response_text.upper():
                return []

            # Extract PDF links from response
            pdf_links = []
            lines = response_text.split('\n')
            current_year = 2024
            target_years = list(range(current_year - years + 1, current_year + 1))

            for line in lines:
                line = line.strip()
                # Look for lines starting with "FOUND:" or direct HTTP links
                if line.startswith('FOUND:'):
                    link = line.replace('FOUND:', '').strip()
                    if link.startswith('http') and link.lower().endswith('.pdf'):
                        year = self._extract_year_from_text(link)
                        if not year or year in target_years:
                            pdf_links.append(link)
                elif line.startswith('http') and line.lower().endswith('.pdf'):
                    # Check if it's from the target years
                    year = self._extract_year_from_text(line)
                    if not year or year in target_years:  # Include if no year found or if in target years
                        pdf_links.append(line)

            # Remove duplicates while preserving order
            unique_links = list(dict.fromkeys(pdf_links))

            if unique_links:
                print(f"🎯 Direct search found {len(unique_links)} PDF links")
                for link in unique_links:
                    print(f"   📄 {link}")

            return unique_links

        except Exception as e:
            print(f"Error in direct search: {e}")
            return []

    def _find_holding_company(self, power_plant_name: str) -> Optional[str]:
        """Find the holding company for a power plant using Google Search + Gemini"""
        try:
            # Use LLM with Google Search tool to find holding company
            search_prompt = f"""
            Search for information about "{power_plant_name}" power plant and identify its holding company or parent company.

            Look for:
            - The company that owns or operates this power plant
            - Parent company or holding company name
            - Corporate owner information

            Based on your search results, please respond with ONLY the holding company name, nothing else.
            If you cannot find a clear holding company, respond with "UNKNOWN".

            Examples of good responses:
            - "Southern Company"
            - "Duke Energy"
            - "Exelon Corporation"
            """

            response = self.genai_client.models.generate_content(
                model="gemini-2.0-flash-exp",
                contents=search_prompt,
                config={
                    "tools": [{"google_search": {}}],
                    "temperature": 0.1,
                }
            )

            # Extract the holding company name from the response
            holding_company = response.text.strip()

            if holding_company.upper() == "UNKNOWN" or not holding_company:
                return None

            return holding_company

        except Exception as e:
            print(f"Error finding holding company: {e}")
            return None

    def _find_official_website(self, company_name: str) -> Optional[str]:
        """Find the official website of a company using Google Search + Gemini"""
        try:
            search_prompt = f"""
            Search for the official website of "{company_name}".

            Look for the main corporate website, not subsidiary sites.

            Please respond with ONLY the main website URL (like https://example.com), nothing else.
            If you cannot find a clear official website, respond with "UNKNOWN".

            Examples of good responses:
            - "https://www.southerncompany.com"
            - "https://www.duke-energy.com"
            - "https://www.exeloncorp.com"
            """

            response = self.genai_client.models.generate_content(
                model="gemini-2.0-flash-exp",
                contents=search_prompt,
                config={
                    "tools": [{"google_search": {}}],
                    "temperature": 0.1,
                }
            )

            website = response.text.strip()

            if website.upper() == "UNKNOWN" or not website:
                return None

            # Validate and format URL
            if not website.startswith(('http://', 'https://')):
                website = 'https://' + website

            return website

        except Exception as e:
            print(f"Error finding official website: {e}")
            return None

    def _find_financial_sections(self, website_url: str) -> List[str]:
        """Find financial/investor relations sections on a website"""
        financial_urls = []

        try:
            base_url = f"{urlparse(website_url).scheme}://{urlparse(website_url).netloc}"

            # Common financial section URLs to try first
            common_paths = [
                '/investors', '/investor-relations', '/investor', '/financials',
                '/annual-reports', '/sec-filings', '/reports', '/corporate-governance',
                '/about/investors', '/about/investor-relations', '/company/investors',
                '/investor-center', '/shareholders', '/financial-information',
                '/corporate/investors', '/corporate/investor-relations', '/ir',
                '/financial-reports', '/annual-report', '/investor-info'
            ]

            # Try common paths first
            for path in common_paths:
                test_url = base_url + path
                try:
                    response = requests.head(test_url, headers=self.headers, timeout=5)
                    if response.status_code == 200:
                        financial_urls.append(test_url)
                except:
                    continue

            # If we found some URLs from common paths, return them
            if financial_urls:
                return financial_urls[:3]  # Limit to first 3 to avoid too many requests

            # Otherwise, scrape the main page for financial links
            try:
                response = requests.get(website_url, headers=self.headers, timeout=10)
                response.raise_for_status()

                soup = BeautifulSoup(response.content, 'html.parser')

                # Find links that might lead to financial sections
                for link in soup.find_all('a', href=True):
                    href = link.get('href')
                    text = link.get_text().lower().strip()

                    # Check if link text contains financial keywords
                    if any(keyword in text for keyword in self.financial_keywords):
                        full_url = urljoin(base_url, href)
                        if full_url not in financial_urls and len(financial_urls) < 5:
                            financial_urls.append(full_url)

            except Exception as e:
                print(f"Error scraping main page: {e}")

        except Exception as e:
            print(f"Error finding financial sections: {e}")

        return financial_urls

    def _extract_annual_report_pdfs(self, financial_urls: List[str], years: int) -> List[str]:
        """Extract annual report PDF links from financial pages"""
        pdf_links = []
        current_year = 2024
        target_years = list(range(current_year - years + 1, current_year + 1))

        for url in financial_urls:
            try:
                print(f"🔍 Scanning: {url}")
                response = requests.get(url, headers=self.headers, timeout=15)
                response.raise_for_status()

                soup = BeautifulSoup(response.content, 'html.parser')
                base_url = f"{urlparse(url).scheme}://{urlparse(url).netloc}"

                # Find all PDF links
                for link in soup.find_all('a', href=True):
                    href = link.get('href')
                    text = link.get_text().strip()

                    # Check if it's a PDF link
                    if href.lower().endswith('.pdf'):
                        full_url = urljoin(base_url, href)

                        # Check if it's an annual report (not financial statement)
                        if self._is_annual_report_pdf(text, href):
                            year = self._extract_year_from_text(text + " " + href)
                            if year and year in target_years:
                                if full_url not in pdf_links:
                                    pdf_links.append(full_url)
                                    print(f"📄 Found: {year} - {full_url}")

                time.sleep(1)  # Be respectful to the server

            except Exception as e:
                print(f"Error extracting PDFs from {url}: {e}")
                continue

        # Sort by year (newest first) and remove duplicates
        unique_links = list(dict.fromkeys(pdf_links))  # Remove duplicates while preserving order
        return unique_links

    def _is_annual_report_pdf(self, text: str, href: str) -> bool:
        """Check if a PDF link is likely an annual report (not financial statement)"""
        combined_text = (text + " " + href).lower()

        # Check for annual report keywords
        has_annual_keywords = any(keyword in combined_text for keyword in self.annual_report_keywords)

        # Check for exclude keywords (financial statements are separate)
        has_exclude_keywords = any(keyword in combined_text for keyword in self.exclude_keywords)

        # If it has annual report keywords and no exclude keywords, it's likely an annual report
        if has_annual_keywords and not has_exclude_keywords:
            return True

        # If it contains "annual" and "report" separately, it's likely an annual report
        if 'annual' in combined_text and 'report' in combined_text and not has_exclude_keywords:
            return True

        # If it contains a year and "report", it might be an annual report
        if 'report' in combined_text and self._extract_year_from_text(combined_text) and not has_exclude_keywords:
            return True

        return False

    def _extract_year_from_text(self, text: str) -> Optional[int]:
        """Extract year from text (looking for 2020-2024)"""
        year_pattern = r'\b(20[2-9][0-9])\b'
        matches = re.findall(year_pattern, text)

        if matches:
            return int(matches[-1])  # Return the last (most recent) year found

        return None


# Example usage and testing
if __name__ == "__main__":
    # Test the dedicated search engine
    search_engine = DedicatedPowerPlantSearchEngine()

    # Test with a power plant name
    test_power_plant = "Vogtle Nuclear Plant"

    print(f"Testing with: {test_power_plant}")
    print("=" * 50)

    result = search_engine.search_power_plant_pdfs(test_power_plant, years=5)

    print(f"\n🏭 Power Plant: {result.power_plant_name}")
    print(f"🏢 Holding Company: {result.holding_company}")
    print(f"✅ Success: {result.success}")

    if result.success:
        print(f"📄 Found {len(result.pdf_links)} PDF links:")
        for i, link in enumerate(result.pdf_links, 1):
            print(f"  {i}. {link}")
    else:
        print(f"❌ Error: {result.error_message}")

    print("\n" + "=" * 50)
    print("Search completed!")
