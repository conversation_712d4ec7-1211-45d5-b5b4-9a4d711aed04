#!/usr/bin/env python3
"""
Example script showing how to use the research agent
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from research_agent import main

if __name__ == "__main__":
    # Override sys.argv to provide example arguments
    original_argv = sys.argv
    
    print("🔬 Running example research query...")
    print("Query: 'What are the latest developments in renewable energy?'")
    print("-" * 50)
    
    # Set up arguments for example
    sys.argv = [
        "research_agent.py",
        "What are the latest developments in renewable energy?",
        "--initial-queries", "2",
        "--max-loops", "1"
    ]
    
    try:
        main()
    except SystemExit:
        pass
    finally:
        # Restore original argv
        sys.argv = original_argv
    
    print("\n" + "=" * 50)
    print("Example completed! To run your own queries:")
    print("python research_agent.py \"your question here\"")