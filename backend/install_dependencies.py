#!/usr/bin/env python3
"""
Install Dependencies Script
Installs all required packages for the power plant search engine.
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """Install all required dependencies"""
    print("🔧 Installing dependencies for Power Plant Search Engine...")
    print("=" * 60)
    
    # List of required packages
    packages = [
        "pydantic>=2.0.0",
        "langgraph>=0.2.6",
        "langchain>=0.3.19",
        "langchain-google-genai",
        "python-dotenv>=1.0.1",
        "google-genai",
        "requests>=2.31.0",
        "beautifulsoup4>=4.12.0",
        "lxml>=4.9.0"
    ]
    
    failed_packages = []
    
    for package in packages:
        print(f"📦 Installing {package}...")
        if install_package(package):
            print(f"✅ {package} installed successfully")
        else:
            print(f"❌ Failed to install {package}")
            failed_packages.append(package)
    
    print("\n" + "=" * 60)
    
    if failed_packages:
        print("❌ Some packages failed to install:")
        for package in failed_packages:
            print(f"  - {package}")
        print("\nTry installing them manually:")
        for package in failed_packages:
            print(f"pip install {package}")
    else:
        print("🎉 All dependencies installed successfully!")
        
        # Check for .env file
        env_file = ".env"
        if not os.path.exists(env_file):
            print("\n📄 Creating .env file...")
            with open(env_file, 'w') as f:
                f.write("GEMINI_API_KEY=your_gemini_api_key_here\n")
            print("✅ .env file created")
            print("⚠️  Please edit the .env file and add your GEMINI_API_KEY")
        
        print("\n🚀 Setup complete! You can now run:")
        print("python3 simple_power_plant_search.py \"Power Plant Name\"")
        print("\nExample:")
        print("python3 simple_power_plant_search.py \"Seil Power Plant\"")

if __name__ == "__main__":
    main()
