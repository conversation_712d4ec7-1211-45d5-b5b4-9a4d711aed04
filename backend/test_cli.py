#!/usr/bin/env python3
"""
Test script to verify the CLI research agent works
"""

import sys
import os
import argparse

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test that all required modules can be imported"""
    try:
        from agent.graph import graph
        print("✅ Core graph import successful")
        
        from agent.power_plant_search import PowerPlantSearchEngine
        print("✅ Power plant search import successful")
        
        from agent.state import State
        print("✅ State import successful")
        
        from agent.tools_and_schemas import web_search_tool
        print("✅ Tools import successful")
        
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_env_setup():
    """Test environment setup"""
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key or api_key == 'your_gemini_api_key_here':
            print("⚠️  GEMINI_API_KEY not properly set in .env file")
            return False
        else:
            print("✅ GEMINI_API_KEY is configured")
            return True
    except Exception as e:
        print(f"❌ Environment setup failed: {e}")
        return False

def main():
    """Run tests"""
    parser = argparse.ArgumentParser(description="Test the research agent CLI")
    parser.add_argument("--skip-env", action="store_true", help="Skip environment test")
    args = parser.parse_args()
    
    print("🧪 Testing Research Agent CLI...")
    
    # Test imports
    if not test_imports():
        print("❌ Import tests failed")
        sys.exit(1)
    
    # Test environment (optional)
    if not args.skip_env:
        if not test_env_setup():
            print("⚠️  Environment tests failed - make sure to set GEMINI_API_KEY in .env")
            print("    You can skip this test with --skip-env")
            sys.exit(1)
    
    print("\n🎉 All tests passed!")
    print("\nYou can now run the research agent:")
    print("python research_agent.py \"your research question\"")

if __name__ == "__main__":
    main()