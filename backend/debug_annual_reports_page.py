#!/usr/bin/env python3
"""
Debug tool to examine what's on the annual reports page
"""

import requests
from bs4 import BeautifulSoup
import re
from urllib.parse import urljoin

def debug_page(url):
    """Debug what's actually on the page"""
    print(f"🔍 Debugging: {url}")
    print("=" * 80)
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=15)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        base_url = f"{url.split('/')[0]}//{url.split('/')[2]}"
        
        print(f"✅ Successfully loaded page")
        print(f"📄 Page title: {soup.title.string if soup.title else 'No title'}")
        print(f"📊 Page size: {len(response.content)} bytes")
        
        # Look for all links
        print(f"\n🔗 All links on the page:")
        links = soup.find_all('a', href=True)
        for i, link in enumerate(links[:20], 1):  # Show first 20 links
            href = link.get('href')
            text = link.get_text().strip()[:100]  # Truncate long text
            full_url = urljoin(base_url, href)
            print(f"  {i}. {text} -> {full_url}")
        
        if len(links) > 20:
            print(f"  ... and {len(links) - 20} more links")
        
        # Look specifically for PDF links
        print(f"\n📄 PDF links found:")
        pdf_count = 0
        for link in links:
            href = link.get('href')
            if href and '.pdf' in href.lower():
                text = link.get_text().strip()
                full_url = urljoin(base_url, href)
                pdf_count += 1
                print(f"  📄 {text} -> {full_url}")
        
        if pdf_count == 0:
            print("  ❌ No direct PDF links found")
        
        # Look for download buttons or other elements
        print(f"\n🔍 Looking for download elements:")
        download_selectors = [
            'button[onclick*=".pdf"]',
            'div[data-file*=".pdf"]',
            'span[onclick*=".pdf"]',
            'a[onclick*=".pdf"]',
            '*[href*="download"]',
            '*[data-url*=".pdf"]'
        ]
        
        for selector in download_selectors:
            elements = soup.select(selector)
            if elements:
                print(f"  Found {len(elements)} elements matching '{selector}':")
                for elem in elements[:5]:  # Show first 5
                    print(f"    {elem}")
        
        # Look for any mention of PDF in the page text
        print(f"\n📝 PDF mentions in page text:")
        page_text = soup.get_text()
        pdf_mentions = re.findall(r'[^\s]*\.pdf[^\s]*', page_text, re.IGNORECASE)
        if pdf_mentions:
            for mention in set(pdf_mentions[:10]):  # Show unique mentions, max 10
                print(f"  📄 {mention}")
        else:
            print("  ❌ No PDF mentions found in page text")
        
        # Look for JavaScript that might load PDFs
        print(f"\n🔧 JavaScript analysis:")
        scripts = soup.find_all('script')
        js_pdf_count = 0
        for script in scripts:
            if script.string and '.pdf' in script.string.lower():
                js_pdf_count += 1
                # Show snippet of JS that mentions PDF
                snippet = script.string[:200] + "..." if len(script.string) > 200 else script.string
                print(f"  📄 JS mentions PDF: {snippet}")
        
        if js_pdf_count == 0:
            print("  ❌ No PDF mentions found in JavaScript")
        
        # Look for common annual report patterns
        print(f"\n📊 Annual report patterns:")
        annual_patterns = [
            r'annual\s+report',
            r'laporan\s+tahunan',
            r'20\d{2}',  # Years
            r'report.*20\d{2}',
            r'20\d{2}.*report'
        ]
        
        for pattern in annual_patterns:
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                unique_matches = list(set(matches))[:5]  # Show unique matches, max 5
                print(f"  📄 Pattern '{pattern}': {unique_matches}")
        
    except Exception as e:
        print(f"❌ Error debugging page: {e}")

def main():
    """Debug the PT Suparma annual reports page"""
    url = "https://www.ptsuparmatbk.com/annual-reports"
    debug_page(url)
    
    print(f"\n" + "=" * 80)
    print("🔧 This debug information will help improve the PDF detection logic")

if __name__ == "__main__":
    main()
