#!/usr/bin/env python3
"""
Test script for the dedicated power plant search engine
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """Test that all required modules can be imported"""
    try:
        from agent.dedicated_power_plant_search import DedicatedPowerPlantSearchEngine, SearchResult
        print("✅ Dedicated search engine import successful")
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False

def test_env_setup():
    """Test environment setup"""
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key or api_key == 'your_gemini_api_key_here':
            print("⚠️  GEMINI_API_KEY not properly set in .env file")
            return False
        else:
            print("✅ GEMINI_API_KEY is configured")
            return True
    except Exception as e:
        print(f"❌ Environment setup failed: {e}")
        return False

def test_search_engine_init():
    """Test search engine initialization"""
    try:
        from agent.dedicated_power_plant_search import DedicatedPowerPlantSearchEngine
        search_engine = DedicatedPowerPlantSearchEngine()
        print("✅ Search engine initialization successful")
        return True
    except Exception as e:
        print(f"❌ Search engine initialization failed: {e}")
        return False

def run_sample_search():
    """Run a sample search to test functionality"""
    try:
        from agent.dedicated_power_plant_search import DedicatedPowerPlantSearchEngine
        
        print("\n🧪 Running sample search...")
        search_engine = DedicatedPowerPlantSearchEngine()
        
        # Test with a well-known power plant
        test_plant = "Vogtle Nuclear Plant"
        print(f"Testing with: {test_plant}")
        
        result = search_engine.search_power_plant_pdfs(test_plant, years=2)
        
        print(f"Power Plant: {result.power_plant_name}")
        print(f"Holding Company: {result.holding_company}")
        print(f"Success: {result.success}")
        
        if result.success:
            print(f"PDF Links Found: {len(result.pdf_links)}")
            for i, link in enumerate(result.pdf_links[:3], 1):  # Show first 3
                print(f"  {i}. {link}")
        else:
            print(f"Error: {result.error_message}")
        
        print("✅ Sample search completed")
        return True
        
    except Exception as e:
        print(f"❌ Sample search failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Dedicated Power Plant Search Engine...")
    print("=" * 50)
    
    # Test imports
    if not test_imports():
        print("❌ Import tests failed")
        sys.exit(1)
    
    # Test environment
    if not test_env_setup():
        print("⚠️  Environment tests failed - make sure to set GEMINI_API_KEY in .env")
        print("    You can still run other tests, but search functionality won't work")
        
        # Ask user if they want to continue
        try:
            response = input("\nContinue with remaining tests? (y/n): ").lower()
            if response != 'y':
                sys.exit(1)
        except KeyboardInterrupt:
            sys.exit(1)
    
    # Test search engine initialization
    if not test_search_engine_init():
        print("❌ Search engine initialization failed")
        sys.exit(1)
    
    # Ask if user wants to run sample search (requires API key)
    if os.getenv('GEMINI_API_KEY') and os.getenv('GEMINI_API_KEY') != 'your_gemini_api_key_here':
        try:
            response = input("\nRun sample search? This will make API calls (y/n): ").lower()
            if response == 'y':
                run_sample_search()
        except KeyboardInterrupt:
            pass
    
    print("\n🎉 All tests completed!")
    print("\nYou can now use the dedicated search engine:")
    print("python dedicated_power_plant_cli.py \"Power Plant Name\"")
    print("\nExample:")
    print("python dedicated_power_plant_cli.py \"Vogtle Nuclear Plant\"")

if __name__ == "__main__":
    main()
