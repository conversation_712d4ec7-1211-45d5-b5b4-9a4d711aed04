#!/usr/bin/env python3

import requests
from bs4 import BeautifulSoup

url = 'https://www.ptsuparmatbk.com/annual-reports'
headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}

print(f"Checking: {url}")

try:
    response = requests.get(url, headers=headers, timeout=15)
    print(f"Status: {response.status_code}")
    
    soup = BeautifulSoup(response.content, 'html.parser')
    print(f"Title: {soup.title.string if soup.title else 'No title'}")
    
    # Count all links
    all_links = soup.find_all('a', href=True)
    print(f"Total links: {len(all_links)}")
    
    # Look for PDF links
    pdf_links = []
    for link in all_links:
        href = link.get('href', '')
        if '.pdf' in href.lower():
            pdf_links.append((link.get_text().strip(), href))
    
    print(f"PDF links found: {len(pdf_links)}")
    for i, (text, href) in enumerate(pdf_links, 1):
        print(f"  {i}. {text} -> {href}")
    
    # Look for any text mentioning years
    page_text = soup.get_text()
    years = []
    import re
    year_matches = re.findall(r'\b(20[12][0-9])\b', page_text)
    years = list(set(year_matches))
    print(f"Years mentioned: {sorted(years)}")
    
except Exception as e:
    print(f"Error: {e}")
