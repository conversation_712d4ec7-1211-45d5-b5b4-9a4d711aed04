#!/usr/bin/env python3
"""
Test script for the Power Plant Search functionality
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from agent.power_plant_search import PowerPlantSearchEngine


def test_power_plant_search():
    """Test the power plant search functionality"""
    
    # Test power plants
    test_plants = [
        "Vogtle Nuclear Plant",
        "Palo Verde Nuclear Generating Station",
        "Diablo Canyon Power Plant",
        "Three Mile Island Nuclear Station"
    ]
    
    search_engine = PowerPlantSearchEngine()
    
    for plant_name in test_plants:
        print(f"\n{'='*60}")
        print(f"Testing: {plant_name}")
        print(f"{'='*60}")
        
        try:
            # Test the complete search pipeline
            result = search_engine.search_power_plant_annual_reports(plant_name, years=3)
            
            if result["search_successful"]:
                print(f"✅ SUCCESS")
                print(f"🏭 Power Plant: {result['power_plant_name']}")
                print(f"🏢 Holding Company: {result['holding_company']}")
                print(f"📊 Annual Reports Found: {len(result['annual_reports'])}")
                
                if result['annual_reports']:
                    print(f"\n📄 Reports:")
                    for i, report in enumerate(result['annual_reports'], 1):
                        print(f"  {i}. {report.title} ({report.year})")
                        print(f"     PDF: {report.pdf_url}")
                
                print(f"\n🔗 PDF Links:")
                for i, link in enumerate(result['pdf_links'], 1):
                    print(f"  {i}. {link}")
                    
            else:
                print(f"❌ FAILED")
                print(f"Error: {result.get('error_message', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ EXCEPTION: {str(e)}")
            
        print(f"\n{'='*60}")


def test_individual_functions():
    """Test individual functions"""
    
    search_engine = PowerPlantSearchEngine()
    
    # Test 1: Holding company search
    print("\n🧪 Testing holding company search...")
    plant_name = "Vogtle Nuclear Plant"
    
    try:
        power_plant_info = search_engine.search_power_plant_holding_company(plant_name)
        
        if power_plant_info:
            print(f"✅ Found holding company: {power_plant_info.holding_company}")
            
            # Test 2: Annual reports search
            print(f"\n🧪 Testing annual reports search for {power_plant_info.holding_company}...")
            annual_reports = search_engine.search_annual_reports(power_plant_info.holding_company, years=2)
            
            if annual_reports:
                print(f"✅ Found {len(annual_reports)} annual reports")
                for report in annual_reports:
                    print(f"  - {report.year}: {report.title}")
                    print(f"    PDF: {report.pdf_url}")
            else:
                print(f"❌ No annual reports found")
                
        else:
            print(f"❌ No holding company found for {plant_name}")
            
    except Exception as e:
        print(f"❌ Exception during testing: {str(e)}")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Test Power Plant Search functionality")
    parser.add_argument("--individual", action="store_true", help="Test individual functions")
    parser.add_argument("--plant", type=str, help="Test specific power plant")
    
    args = parser.parse_args()
    
    if args.plant:
        # Test specific power plant
        search_engine = PowerPlantSearchEngine()
        print(f"Testing specific power plant: {args.plant}")
        
        result = search_engine.search_power_plant_annual_reports(args.plant, years=5)
        
        if result["search_successful"]:
            print(f"✅ SUCCESS")
            print(f"🏭 Power Plant: {result['power_plant_name']}")
            print(f"🏢 Holding Company: {result['holding_company']}")
            print(f"📊 Annual Reports Found: {len(result['annual_reports'])}")
            
            print(f"\n🔗 PDF Links:")
            for i, link in enumerate(result['pdf_links'], 1):
                print(f"  {i}. {link}")
        else:
            print(f"❌ FAILED")
            print(f"Error: {result.get('error_message', 'Unknown error')}")
            
    elif args.individual:
        test_individual_functions()
    else:
        test_power_plant_search()