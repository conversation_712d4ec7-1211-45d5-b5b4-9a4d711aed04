#!/usr/bin/env python3
"""
Test script to check holding company search accuracy
"""

import os
import sys
from google.genai import Client
from dotenv import load_dotenv

load_dotenv()

def test_holding_company_search(power_plant_name):
    """Test holding company search for a specific power plant"""
    try:
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            print("❌ GEMINI_API_KEY not set")
            return None

        client = Client(api_key=api_key)

        # Test with detailed search prompt
        search_prompt = f"""
        Search for information about "{power_plant_name}" power plant and identify its holding company or parent company.
        
        Look for:
        - The company that owns or operates this power plant
        - Parent company or holding company name
        - Corporate owner information
        - Utility company that operates this plant
        
        Please respond with ONLY the holding company name, nothing else.
        If you cannot find a clear holding company, respond with "UNKNOWN".
        
        Examples of good responses:
        - "Southern Company"
        - "Duke Energy"
        - "Exelon Corporation"
        """

        print(f"🔍 Searching for holding company of: {power_plant_name}")
        print(f"📋 Search query: '{power_plant_name} power plant holding company'")
        
        response = client.models.generate_content(
            model="gemini-2.0-flash-exp",
            contents=search_prompt,
            config={
                "tools": [{"google_search": {}}],
                "temperature": 0.1,
            }
        )

        print(f"🤖 Raw Gemini response:")
        print(f"'{response.text}'")
        print()
        
        holding_company = response.text.strip()
        
        if holding_company.upper() == "UNKNOWN" or not holding_company:
            print(f"❌ No holding company found")
            return None
        
        print(f"✅ Extracted holding company: '{holding_company}'")
        return holding_company

    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def main():
    """Test holding company search with various power plants"""
    
    test_cases = [
        "Vogtle Nuclear Plant",
        "Three Mile Island",
        "Palo Verde Nuclear Plant", 
        "Diablo Canyon Power Plant",
        "Seil Power Plant",
        "Mailao Power Plant",
        "Taichung Power Plant"
    ]
    
    print("🧪 Testing Holding Company Search Accuracy")
    print("=" * 60)
    
    for power_plant in test_cases:
        print(f"\n📋 Test Case: {power_plant}")
        print("-" * 40)
        
        holding_company = test_holding_company_search(power_plant)
        
        if holding_company:
            print(f"🎯 Result: {power_plant} → {holding_company}")
        else:
            print(f"❌ Failed to find holding company for {power_plant}")
        
        print()
    
    print("=" * 60)
    print("🔍 Analysis:")
    print("- Check if the holding companies found are accurate")
    print("- Verify if the search is finding the correct parent companies")
    print("- Look for any patterns in failures")

if __name__ == "__main__":
    main()
