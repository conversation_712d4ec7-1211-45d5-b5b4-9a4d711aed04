#!/usr/bin/env python3
"""Test the improvements for power plant search"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from agent.power_plant_search import PowerPlantSearchEngine

def test_seil_power_plant():
    """Test the search for Seil power plant"""
    
    try:
        search_engine = PowerPlantSearchEngine()
        
        # Test holding company search
        print("Testing holding company search...")
        power_plant_info = search_engine.search_power_plant_holding_company("seil power plant")
        
        if power_plant_info:
            print(f"✅ Found holding company: {power_plant_info.holding_company}")
            
            # Test annual reports search
            print("\nTesting annual reports search...")
            annual_reports = search_engine.search_annual_reports(power_plant_info.holding_company, 5)
            
            if annual_reports:
                print(f"✅ Found {len(annual_reports)} annual reports")
                for report in annual_reports:
                    print(f"  - {report.year}: {report.title}")
                    print(f"    PDF: {report.pdf_url}")
            else:
                print("❌ No annual reports found")
                print("This is expected for some companies that don't publish reports publicly")
        else:
            print("❌ No holding company found")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_seil_power_plant()