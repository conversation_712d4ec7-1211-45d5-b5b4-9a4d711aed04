#!/usr/bin/env python3
"""
Test the improved search engine with PT Suparma case
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_pt_suparma():
    """Test with PT Suparma case that was failing"""
    try:
        from agent.dedicated_power_plant_search import DedicatedPowerPlantSearchEngine
        
        print("🧪 Testing improved search with PT Suparma...")
        print("=" * 60)
        
        search_engine = DedicatedPowerPlantSearchEngine()
        
        # Test with the case that was failing
        result = search_engine.search_power_plant_pdfs("PLTU Suparma", years=5)
        
        print(f"\n🏭 Power Plant: {result.power_plant_name}")
        print(f"🏢 Holding Company: {result.holding_company}")
        print(f"✅ Success: {result.success}")
        
        if result.success:
            print(f"📄 PDF Links Found: {len(result.pdf_links)}")
            for i, link in enumerate(result.pdf_links, 1):
                print(f"  {i}. {link}")
        else:
            print(f"❌ Error: {result.error_message}")
        
        return result.success
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    """Run the test"""
    print("🔧 Testing Improved PDF Detection...")
    
    # Check environment
    if not os.getenv("GEMINI_API_KEY"):
        print("❌ GEMINI_API_KEY not set")
        print("Run: export GEMINI_API_KEY='your_key_here'")
        return
    
    success = test_pt_suparma()
    
    if success:
        print("\n🎉 Test passed! Improved search is working.")
    else:
        print("\n⚠️ Test shows there are still issues to resolve.")
    
    print("\nThe improvements include:")
    print("✅ Enhanced PDF detection methods")
    print("✅ International keyword support")
    print("✅ Multiple search strategies")
    print("✅ Better handling of download buttons and embedded PDFs")

if __name__ == "__main__":
    main()
