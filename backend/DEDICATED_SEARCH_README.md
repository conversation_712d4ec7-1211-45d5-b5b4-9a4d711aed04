# Dedicated Power Plant Search Engine

A specialized search engine designed for one specific use case: finding annual report PDFs for power plants.

## Use Case

**Input:** Power plant name from user  
**Output:** PDF links for annual reports from the past 5 years

## How It Works

**Two-Step Search Strategy:**

### Step 1: Direct Search
1. **Direct Annual Report Search** → Searches "{power plant name} annual report" for direct PDF links
2. **Quick Results** → If PDFs found, returns them immediately

### Step 2: Holding Company Search (Fallback)
1. **Find Holding Company** → Searches "{power plant name} power plant holding company" using Google Search + Gemini
2. **Find Official Website** → Locates the holding company's official website
3. **Scrape Financial Sections** → Finds investor relations, financial statements, or financials sections
4. **Extract PDF Links** → Identifies and extracts annual report PDF links (not financial statements)
5. **Filter by Years** → Returns only PDFs from the past 5 years

## Key Features

- **Two-Step Search**: First tries direct search, then falls back to holding company approach
- **PDF Links Only**: Returns direct PDF download links, not webpage links
- **Annual Reports Focus**: Distinguishes between annual reports and financial statements
- **5-Year Scope**: Automatically filters for reports from the past 5 years
- **Smart Fallback**: If direct search fails, automatically tries holding company method
- **Error Handling**: Provides clear error messages when searches fail
- **Respectful Scraping**: Includes delays and proper headers to be respectful to websites

## Installation

1. **Set up environment:**
   ```bash
   cd backend
   pip install -e .
   ```

2. **Configure API key:**
   ```bash
   # Edit .env file and add your GEMINI_API_KEY
   echo "GEMINI_API_KEY=your_api_key_here" >> .env
   ```

## Usage

### Command Line Interface

```bash
# Basic usage
python dedicated_power_plant_cli.py "Vogtle Nuclear Plant"

# Specify number of years
python dedicated_power_plant_cli.py "Three Mile Island" --years 3

# Verbose output
python dedicated_power_plant_cli.py "Seil Power Plant" --verbose
```

### Python API

```python
from agent.dedicated_power_plant_search import DedicatedPowerPlantSearchEngine

# Initialize search engine
search_engine = DedicatedPowerPlantSearchEngine()

# Search for PDFs
result = search_engine.search_power_plant_pdfs("Vogtle Nuclear Plant", years=5)

if result.success:
    print(f"Holding Company: {result.holding_company}")
    print(f"PDF Links: {result.pdf_links}")
else:
    print(f"Error: {result.error_message}")
```

## Example Output

```
🔍 Searching for: Vogtle Nuclear Plant
🏢 Found holding company: Southern Company
🌐 Found website: https://www.southerncompany.com
📊 Found 3 financial sections
📄 Found 5 PDF links

🏭 Power Plant: Vogtle Nuclear Plant
🏢 Holding Company: Southern Company
✅ Success: True
📄 Found 5 PDF links:
  1. https://www.southerncompany.com/content/dam/southern-company/pdf/investor-relations/annual-reports/2023-annual-report.pdf
  2. https://www.southerncompany.com/content/dam/southern-company/pdf/investor-relations/annual-reports/2022-annual-report.pdf
  3. https://www.southerncompany.com/content/dam/southern-company/pdf/investor-relations/annual-reports/2021-annual-report.pdf
  4. https://www.southerncompany.com/content/dam/southern-company/pdf/investor-relations/annual-reports/2020-annual-report.pdf
  5. https://www.southerncompany.com/content/dam/southern-company/pdf/investor-relations/annual-reports/2019-annual-report.pdf
```

## Testing

```bash
# Test the search engine
python test_dedicated_search.py

# Test with a specific power plant
python dedicated_power_plant_cli.py "Vogtle Nuclear Plant"
```

## Technical Details

### Search Strategy

1. **Holding Company Discovery**: Uses Google Search API with Gemini to find the parent company
2. **Website Discovery**: Searches for official corporate websites
3. **Financial Section Detection**: Looks for common paths like `/investors`, `/investor-relations`, `/financials`
4. **PDF Extraction**: Scrapes pages for PDF links with annual report keywords
5. **Filtering**: Excludes quarterly reports, financial statements, and old reports

### Keywords Used

**Annual Report Keywords:**
- annual report, annual-report, yearly report
- 10-k, form 10-k, annual filing
- ar/, /ar/, annual

**Excluded Keywords:**
- quarterly, q1, q2, q3, q4, interim
- financial statement, cash flow, balance sheet
- proxy, income statement

### Error Handling

- **No Holding Company Found**: When the power plant's parent company cannot be identified
- **No Website Found**: When the holding company's official website cannot be located
- **No Financial Sections**: When investor relations pages cannot be found
- **No PDFs Found**: When annual report PDFs are not available or accessible

## Limitations

- Requires publicly available annual reports
- Some companies may use different website structures
- International companies may have different reporting requirements
- Rate limited to be respectful to websites (includes delays)
- Depends on Google Search API availability

## Files

- `src/agent/dedicated_power_plant_search.py` - Main search engine
- `dedicated_power_plant_cli.py` - Command line interface
- `test_dedicated_search.py` - Test script
- `DEDICATED_SEARCH_README.md` - This documentation
